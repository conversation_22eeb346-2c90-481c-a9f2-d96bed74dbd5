<?xml version="1.0" encoding="utf-8"?>
<odoo>

        <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.auth.signup</field>
            <field name="model">res.config.settings</field>
            <field name="inherit_id" ref="base_setup.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//setting[@id='access_rights']" position="before">
                    <setting id="login_documents" title="To send invitations in B2B mode, open a contact or select several ones in list view and click on 'Portal Access Management' option in the dropdown menu *Action*." help="Let your customers log in to see their documents">
                        <field name="auth_signup_uninvited" class="o_light_label" widget="radio" options="{'horizontal': true}" required="True"/>
                        <div class="content-group" invisible="auth_signup_uninvited == 'b2b'">
                            <div class="mt8">
                                <button type="object" name="action_open_template_user" string="Default Access Rights" icon="oi-arrow-right" class="btn-link"/>
                            </div>
                        </div>
                    </setting>
                    <setting string="Password Reset" help="Enable password reset from Login page" id="enable_password_reset">
                        <field name="auth_signup_reset_password"/>
                    </setting>
                </xpath>
            </field>
        </record>

</odoo>
