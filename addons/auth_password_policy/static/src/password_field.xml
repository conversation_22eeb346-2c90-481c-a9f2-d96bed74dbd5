<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="auth_password_policy.PasswordField">
        <span t-if="props.readonly" t-out="props.record.data[props.name] and '*'.repeat(props.record.data[props.name].length)"/>
        <t t-else="">
            <input class="o_input o_field_password" type="password"
                t-att-id="props.id" t-ref="input" placeholder=" "
                t-on-input="ev => this.state.value = ev.target.value"/>
            <Meter password="state.value"
                required="state.required"
                recommended="recommendations"/>
        </t>
    </t>
</templates>
