<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="decimal_percentage_analytic" model="decimal.precision">
            <field name="name">Percentage Analytic</field>
            <field name="digits" eval="2"/>
        </record>

        <!--
            Save which plan is used for Projects. Once it is set, even if not used, it cannot be changed safely without a SQL script.
            The other plans will generate dynamic columns, so changing this param would require renaming the columns also.
        -->
        <function model="ir.config_parameter" name="set_param" eval="('analytic.project_plan', '1')"/>
        <record id="analytic_plan_projects" model="account.analytic.plan" forcecreate="0">
            <field name="name">Project</field>
            <field name="default_applicability">optional</field>
        </record>
    </data>
</odoo>
