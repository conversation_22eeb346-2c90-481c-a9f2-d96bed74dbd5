<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data noupdate="1">

    <record id="analytic_comp_rule" model="ir.rule">
        <field name="name">Analytic multi company rule</field>
        <field name="model_id" ref="model_account_analytic_account"/>
        <field eval="True" name="global"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id', 'parent_of', company_ids)]</field>
    </record>

    <record id="analytic_line_comp_rule" model="ir.rule">
        <field name="name">Analytic line multi company rule</field>
        <field name="model_id" ref="model_account_analytic_line"/>
        <field eval="True" name="global"/>
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
    </record>

    <record id="analytic_applicability_comp_rule" model="ir.rule">
        <field name="name">Analytic applicability multi company rule</field>
        <field name="model_id" ref="model_account_analytic_applicability"/>
        <field eval="True" name="global"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id', 'parent_of', company_ids)]</field>
    </record>

    <record id="analytic_distribution_model_comp_rule" model="ir.rule">
        <field name="name">Analytic distribution model multi company rule</field>
        <field name="model_id" ref="model_account_analytic_distribution_model"/>
        <field eval="True" name="global"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id', 'parent_of', company_ids)]</field>
    </record>
</data>
<data noupdate="0">

    <record id="group_analytic_accounting" model="res.groups">
        <field name="name">Analytic Accounting</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

</data>
</odoo>
