<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record model="accounting.assert.test" id="account_test_01">
        <field name="sequence">1</field>
        <field name="name">Test 1: General balance</field>
        <field name="desc">Check the balance: Debit sum = Credit sum</field>
        <field name="code_exec"><![CDATA[sql="""SELECT
sum(debit)-sum(credit) as balance
FROM  account_move_line 
"""
cr.execute(sql)
result=[]
res= cr.dictfetchall()
if res[0]['balance']!=0.0 and res[0]['balance'] is not None:
  result.append(_('* The difference of the balance is: '))
  result.append(res)
]]></field>
    </record>

    <record model="accounting.assert.test" id="account_test_03">
        <field name="sequence">3</field>
        <field name="name">Test 3: Movement lines</field>
        <field name="desc">Check if movement lines are balanced and have the same date and period</field>
        <field name="code_exec"><![CDATA[order_columns=['am_date','ml_date','am.date','ml.date','am.id']
sql="""SELECT
  am.id as move_id,
  sum(debit)-sum(credit) as balance,
  am.date,
  ml.date,
  am.date as am_date,
  ml.date as ml_date
FROM account_move am, account_move_line ml
WHERE
  ml.move_id = am.id
GROUP BY am.name, am.id, am.state, am.date, ml.date,am.date, ml.date,am.date, ml.date
HAVING abs(sum(ml.debit-ml.credit)) <> 0 or am.date!=ml.date or (am.date!=ml.date)
"""
cr.execute(sql)
res = cr.dictfetchall()
if res:
    res.insert(0,_('* The test failed for these movement lines:'))
result = res

]]></field>
    </record>

<!-- TODO: rewrite test since the model of reconciliation has changed -->
<!--     <record model="accounting.assert.test" id="account_test_04">
        <field name="sequence">4</field>
        <field name="name">Test 4: Totally reconciled journal items</field>
        <field name="desc">Check if the totally reconciled journal items are balanced</field>
        <field name="code_exec"><![CDATA[res = []
cr.execute("SELECT distinct reconcile_id from account_move_line where reconcile_id is not null")
rec_ids = cr.dictfetchall()
for record in rec_ids :
  cr.execute("SELECT distinct r.name,r.id from account_journal j,account_period p, account_move_reconcile r,account_move m, account_move_line ml where m.journal_id=j.id and m.date=p.id and ml.reconcile_id=%s and ml.move_id=m.id and ml.reconcile_id=r.id group by r.id,r.name having sum(ml.debit)-sum(ml.credit)<>0", (record['reconcile_id'],))
  reconcile_ids=cr.dictfetchall()
  if reconcile_ids:
    res.append(', '.join(["Reconcile name: %(name)s, id=%(id)s " % r for r in reconcile_ids]))
result = res
if result:
    result.insert(0,_('* The test failed for these reconciled items(id/name):'))
]]></field>
    </record> -->

    <record model="accounting.assert.test" id="account_test_05">
        <field name="sequence">5</field>
        <field name="name">Test 5.1 : Payable and Receivable accountant lines of reconciled invoices</field>
        <field name="desc">Check that reconciled invoice for Sales/Purchases has reconciled entries for Payable and Receivable Accounts</field>
        <field name="code_exec"><![CDATA[res = []
cr.execute("SELECT distinct inv.number,inv.id from account_invoice inv, account_move m, account_move_line ml, account_account a where m.id=ml.move_id and ml.account_id=a.id and a.account_type in ('asset_receivable','liability_payable') and inv.move_id=m.id and ml.reconciled is true;")
records= cr.dictfetchall()
rec = [r['id'] for r in records]
res = reconciled_inv()
invoices = set(rec).difference(set(res))
result = [rec for rec in records if rec['id'] in invoices]
if result:
    result.insert(0,_('* Invoices that need to be checked: '))
]]></field>
    </record>

    <record model="accounting.assert.test" id="account_test_05_2">
        <field name="sequence">6</field>
        <field name="name">Test 5.2 : Reconcilied invoices and Payable/Receivable accounts</field>
        <field name="desc">Check that reconciled account moves, that define Payable and Receivable accounts, are belonging to reconciled invoices</field>
        <field name="code_exec"><![CDATA[res = reconciled_inv()
result=[]
if res:
    cr.execute("SELECT distinct inv.number,inv.id from account_invoice inv, account_move_line ml, account_account a, account_move m where m.id=ml.move_id and inv.move_id=m.id and inv.id=inv.move_id and ml.reconciled is false and a.account_type in ('asset_receivable','liability_payable') and ml.account_id=a.id and inv.id in %s",(tuple(res),))
    records = cr.dictfetchall()
    result = [rec for rec in records]
    if result:
        result.insert(0,_('* Invoices that need to be checked: '))
]]></field>
    </record>

    <record model="accounting.assert.test" id="account_test_06">
        <field name="sequence">7</field>
        <field name="name">Test 6 : Invoices status</field>
        <field name="desc">Check that paid/reconciled invoices are not in 'Open' state</field>
        <field name="code_exec"><![CDATA[
from odoo import _
res = []
column_order = ['number','id','name','state']
if reconciled_inv():
  cr.execute("select inv.name,inv.state,inv.id,inv.number from account_invoice inv where inv.state!='paid' and id in %s", (tuple(reconciled_inv()),))
  res = cr.dictfetchall()
result = res
if result:
    result.insert(0,_('* Invoices that need to be checked: '))
]]></field>
    </record>

    <record model="accounting.assert.test" id="account_test_07">
        <field name="sequence">8</field>
        <field name="name">Test 7: Closing balance on bank statements</field>
        <field name="desc">Check on bank statement that the Closing Balance = Starting Balance + sum of statement lines</field>
        <field name="code_exec"><![CDATA[column_order = ['name','difference']
cr.execute("SELECT s.balance_start+sum(m.amount)-s.balance_end_real as difference, s.name from account_bank_statement s inner join account_bank_statement_line m on m.statement_id=s.id group by s.id, s.balance_start, s.balance_end_real,s.name having abs(s.balance_start+sum(m.amount)-s.balance_end_real) > 0.*********;")
result = cr.dictfetchall()
if result:
    result.insert(0,_('* Unbalanced bank statement that need to be checked: '))
]]></field>
    </record>
    <!-- TODO account.period has been removed -->
    <!-- <record model="accounting.assert.test" id="account_test_08">
        <field name="sequence">9</field>
        <field name="name">Test 8: Accounts and partners on account moves</field>
        <field name="desc">Check that general accounts and partners on account moves are active</field>
        <field name="code_exec"><![CDATA[column_order=['partner_name','partner_active','account_name','move_line_id','period']
res = []
cr.execute("SELECT l.id as move_line_id,a.name as account_name,a.code as account_code,r.name as partner_name,r.active as partner_active,p.name as period from account_period p,res_partner r, account_account a,account_move_line l where l.account_id=a.id and l.partner_id=r.id and (not r.active or not a.active) and l.period_id=p.id")
res = cr.dictfetchall()
result = res
if result:
  result.insert(0,_('* Here is the list of inactive partners and movement lines that are not correct: '))
]]></field>
    </record> -->
</odoo>
