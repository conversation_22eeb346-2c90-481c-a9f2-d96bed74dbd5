<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="view_crm_partner_geo_form" model="ir.ui.view">
            <field name="name">res.partner.geolocation.inherit</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_form"/>
            <field name="arch" type="xml">
                <xpath expr="//notebook[last()]" position="inside">
                    <page string="Partner Assignment" name="geo_location">
                        <group>
                            <group string="Geolocation">
                                <label for="date_localization" string="Geo Location"/>
                                <div>
                                    <span>Lat : <field name="partner_latitude" nolabel="1" class="oe_inline"/></span>
                                    <br/>
                                    <span>Long: <field name="partner_longitude" nolabel="1" class="oe_inline"/></span>
                                    <br/>
                                    <span invisible="not date_localization">Updated on:
                                        <field name="date_localization" nolabel="1" readonly="1" class="oe_inline"/>
                                        <br/>
                                    </span>
                                    <div name="geo_localize_button">
                                        <button invisible="partner_latitude != 0 or partner_longitude != 0"
                                                icon="fa-gear" string="Compute based on address" title="Compute Localization"
                                                name="geo_localize" type="object" class="btn btn-link p-0"/>
                                        <button invisible="partner_latitude == 0 and partner_longitude == 0"
                                                icon="fa-refresh" string="Refresh" title="Refresh Localization"
                                                name="geo_localize" type="object" class="btn btn-link p-0"/>
                                    </div>
                                </div>
                            </group>
                        </group>
                    </page>
                </xpath>
            </field>
        </record>
</odoo>
