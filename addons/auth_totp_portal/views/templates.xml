<odoo>
    <template id="totp_portal_hook" name="TOTP Portal hook" inherit_id="portal.portal_my_security">
        <xpath expr="//section[@name='portal_change_password']" position="after">
            <!--
                portal users don't have access to non-qweb views anymore, so
                embed the target view as a *data island* of sorts, a JSON embed
                of the fields_view_get could have been nice but at 14k it'd be a
                bit ridiculous
            -->
            <div class="d-none" id="totp_wizard_view">
                <t t-esc="env.ref('auth_totp.view_totp_wizard').sudo().get_combined_arch()"/>
            </div>
            <section>
                <h4>
                    Two-factor authentication
                    <a href="https://www.odoo.com/documentation/master/applications/general/auth/2fa.html" target="_blank">
                        <i title="Documentation" class="fa fa-fw o_button_icon fa-info-circle"></i>
                    </a>
                </h4>
                <t t-if="not user_id.totp_enabled">
                    <div class="alert alert-secondary" role="status">
                        <i class="fa fa-warning"/>
                        Two-factor authentication not enabled
                    </div>
                    <button type="button" class="btn btn-secondary" id="auth_totp_portal_enable">
                        Enable two-factor authentication
                    </button>
                </t>
                <t t-else="">
                    <span class="text-success">
                        <i class="fa fa-check-circle"/>
                        Two-factor authentication enabled
                    </span>
                    <button type="button" class="btn btn-link" id="auth_totp_portal_disable">
                        (Disable two-factor authentication)
                    </button>
                    <t t-if="len(user_id.totp_trusted_device_ids)">
                        <table class="table o_main_table">
                            <thead>
                                <tr>
                                    <th><strong>Trusted Device</strong></th>
                                    <th><strong>Added On</strong></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr t-foreach="user_id.totp_trusted_device_ids" t-as="td">
                                    <td>
                                        <span t-field="td.name"/>
                                    </td>
                                    <td>
                                        <span t-field="td.create_date"/>
                                    </td>
                                    <td>
                                        <i class="fa fa-trash text-danger" type="button" t-att-id="td.id"/>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <button class="btn btn-primary" type="button" id="auth_totp_portal_revoke_all_devices">
                            Revoke All
                        </button>
                    </t>
                </t>
            </section>
        </xpath>
    </template>
</odoo>
