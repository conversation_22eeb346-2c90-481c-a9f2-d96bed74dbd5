<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="peppol_registration_form" model="ir.ui.view">
        <field name="name">peppol.registration.form</field>
        <field name="model">peppol.registration</field>
        <field name="groups_id" eval="[Command.link(ref('account.group_account_manager'))]"/>
        <field name="arch" type="xml">
            <form class="form_peppol_registration">
                <field name="company_id" invisible="1"/>
                <sheet>
                    <div class="m-0" name="warnings" invisible="not peppol_warnings or account_peppol_proxy_state != 'not_registered'">
                        <field name="peppol_warnings" class="o_field_html" widget="actionable_errors"/>
                    </div>

                    <div name="peppol_registration" invisible="account_peppol_proxy_state not in ('not_registered', 'in_verification')">
                        <p class="text-muted">Send electronic invoices, and receive bills automatically via Peppol</p>

                            <!-- Optional step: receiver registration fields -->

                        <group col="1">
                            <group>
                                <field name="smp_registration" string="Allow incoming invoices" invisible="1"/>
                                <field name="peppol_eas"
                                       nolabel="1"
                                       class="o_field_peppol_eas_selection"/>
                                <field name="peppol_endpoint" nolabel="1" placeholder="Your endpoint"/>
                                <field name="contact_email" string="Email"/>
                                <field name="phone_number" required="edi_mode not in ('demo', 'test')" string="Phone"/>
                            </group>

                            <!-- edi mode info -->
                            <field name="edi_mode_constraint" invisible="1"/>
                            <group invisible="account_peppol_proxy_state != 'not_registered'">
                                <field name="edi_mode" invisible="1"/>
                                <div class="text-muted col" invisible="edi_mode != 'prod'">
                                    By clicking the button below I accept that Odoo may process my e-invoices.
                                </div>
                                <div class="text-muted col" invisible="edi_mode != 'test'">
                                    Test mode allows sending e-invoices through the test Peppol network.
                                    By clicking the button below I accept that Odoo may process my e-invoices.
                                </div>
                            </group>
                        </group>
                    </div>
                </sheet>
                <footer>
                    <widget name="peppol_settings_buttons"
                            invisible="account_peppol_proxy_state in ('rejected', False)"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>
