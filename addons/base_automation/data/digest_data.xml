<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <data>
        <record id="digest_tip_base_automation_0" model="digest.tip">
            <field name="name">Tip: Automate everything with Automation Rules</field>
            <field name="sequence">3700</field>
            <field name="group_id" ref="base.group_system"/>
            <field name="tip_description" type="html">
<div>
    <p class="tip_title">Tip: Automate everything with Automation Rules</p>
    <p class="tip_content">Send an email when an object changes state, archive records after a month of inactivity or remind yourself to follow-up on tasks when a specific tag is added.<br/>With Automation Rules, you can automate any workflow.</p>
    <img src="https://download.odoocdn.com/digests/base_automation/static/src/img/18-automation-rules.gif" width="540" class="illustration_border"/>
</div>
            </field>
        </record>
    </data>
</odoo>
