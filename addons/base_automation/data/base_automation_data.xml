<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="ir_cron_data_base_automation_check" model="ir.cron">
            <field name="name">Automation Rules: check and execute</field>
            <field name="model_id" ref="model_base_automation"/>
            <field name="state">code</field>
            <field name="code">model._check(True)</field>
            <field name="interval_number">4</field>
            <field name="interval_type">hours</field>
            <field name="active" eval="False" />
        </record>
    </data>
</odoo>
