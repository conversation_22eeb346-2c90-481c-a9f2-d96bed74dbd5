<?xml version="1.0" encoding="UTF-8"?>

<templates>
    <t t-name="base_automation.TriggerSelectionField" t-inherit="web.SelectionField" t-inherit-mode="primary">
        <xpath expr="//t[@t-foreach='options']" position="replace">
            <t t-foreach="groupedOptions" t-as="group" t-key="group.key">
                <optgroup t-att-label="group.name">
                    <t t-foreach="group.options" t-as="option" t-key="option.value">
                        <option
                            t-att-selected="option.value === value"
                            t-att-value="stringify(option.value)"
                            t-esc="option.label"/>
                    </t>
                </optgroup>
            </t>
        </xpath>
    </t>
</templates>
