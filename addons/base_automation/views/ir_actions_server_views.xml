<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Automation: Server Action Form View -->
    <record id="ir_actions_server_view_form_automation" model="ir.ui.view">
        <field name="name">ir.actions.server.view.form.automation</field>
        <field name="model">ir.actions.server</field>
        <field name="mode">primary</field>
        <field name="priority">32</field>
        <field name="inherit_id" ref="base.view_server_action_form"/>
        <field name="arch" type="xml">
            <xpath expr='//field[@name="name"]' position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr='//field[@name="state"]' position="attributes">
                <attribute name="string">Type</attribute>
            </xpath>
            <xpath expr="//header" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='model_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>
</odoo>
