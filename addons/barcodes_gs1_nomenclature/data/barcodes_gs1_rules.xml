<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="default_gs1_nomenclature" model="barcode.nomenclature">
            <field name="name">Default GS1 Nomenclature</field>
            <field name="is_gs1_nomenclature">true</field>
        </record>

        <!-- Identifier Barcodes -->
        <record id="barcode_rule_gs1_00" model="barcode.rule">
            <field name="name">Serial Shipping Container Code</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">100</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(00)(\d{18})</field>
            <field name="type">package</field>
            <field name="gs1_content_type">identifier</field>
        </record>

        <record id="barcode_rule_gs1_01" model="barcode.rule">
            <field name="name">Global Trade Item Number (GTIN)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">101</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(01)(\d{14})</field>
            <field name="type">product</field>
            <field name="gs1_content_type">identifier</field>
        </record>

        <record id="barcode_rule_gs1_02" model="barcode.rule">
            <field name="name">GTIN of contained trade items</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">102</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(02)(\d{14})</field>
            <field name="type">product</field>
            <field name="gs1_content_type">identifier</field>
        </record>

        <record id="barcode_rule_gs1_410" model="barcode.rule">
            <field name="name">Ship to / Deliver to Global Location Number (GLN)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">110</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(410)(\d{13})</field>
            <field name="type">location_dest</field>
            <field name="gs1_content_type">identifier</field>
        </record>

        <record id="barcode_rule_gs1_413" model="barcode.rule">
            <field name="name">Ship for / Deliver for - Forward to Global Location Number (GLN)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">113</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(413)(\d{13})</field>
            <field name="type">location_dest</field>
            <field name="gs1_content_type">identifier</field>
        </record>

        <record id="barcode_rule_gs1_414" model="barcode.rule">
            <field name="name">Identification of a physical location - Global Location Number (GLN)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">114</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(414)(\d{13})</field>
            <field name="type">location</field>
            <field name="gs1_content_type">identifier</field>
        </record>

        <!-- Alphanumeric Barcodes -->
        <record id="barcode_rule_gs1_10" model="barcode.rule">
            <field name="name">Batch or lot number</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">125</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(10)([!"%-/0-9:-?A-Z_a-z]{0,20})</field>
            <field name="type">lot</field>
            <field name="gs1_content_type">alpha</field>
        </record>

        <record id="barcode_rule_gs1_21" model="barcode.rule">
            <field name="name">Serial number</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">126</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(21)([!"%-/0-9:-?A-Z_a-z]{0,20})</field>
            <field name="type">lot</field>
            <field name="gs1_content_type">alpha</field>
        </record>

        <!-- Date Barcodes -->
        <record id="barcode_rule_gs1_13" model="barcode.rule">
            <field name="name">Pack date (YYMMDD)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">137</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(13)(\d{6})</field>
            <field name="type">pack_date</field>
            <field name="gs1_content_type">date</field>
        </record>

        <record id="barcode_rule_gs1_15" model="barcode.rule">
            <field name="name">Best before date (YYMMDD)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">138</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(15)(\d{6})</field>
            <field name="type">use_date</field>
            <field name="gs1_content_type">date</field>
        </record>

        <record id="barcode_rule_gs1_17" model="barcode.rule">
            <field name="name">Expiration date (YYMMDD)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">139</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(17)(\d{6})</field>
            <field name="type">expiration_date</field>
            <field name="gs1_content_type">date</field>
        </record>

        <!-- Quantity/Measure Barcode -->
        <record id="barcode_rule_gs1_30" model="barcode.rule">
            <field name="name">Variable count of items (variable measure trade item)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">300</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(30)(\d{0,8})</field>
            <field name="associated_uom_id" ref="uom.product_uom_unit"/>
            <field name="type">quantity</field>
            <field name="gs1_content_type">measure</field>
            <field name="gs1_decimal_usage">False</field>
        </record>

        <record id="barcode_rule_gs1_37" model="barcode.rule">
            <field name="name">Count of trade items or trade item pieces contained in a logistic unit</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">305</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(37)(\d{0,8})</field>
            <field name="associated_uom_id" ref="uom.product_uom_unit"/>
            <field name="type">quantity</field>
            <field name="gs1_content_type">measure</field>
            <field name="gs1_decimal_usage">False</field>
        </record>

        <record id="barcode_rule_gs1_310y" model="barcode.rule">
            <field name="name">Net weight, kilograms (variable measure trade item)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">310</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(310[0-5])(\d{6})</field>
            <field name="associated_uom_id" ref="uom.product_uom_kgm"/>
            <field name="type">quantity</field>
            <field name="gs1_content_type">measure</field>
            <field name="gs1_decimal_usage">True</field>
        </record>

        <record id="barcode_rule_gs1_311y" model="barcode.rule">
            <field name="name">Length or first dimension, metres (variable measure trade item)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">311</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(311[0-5])(\d{6})</field>
            <field name="associated_uom_id" ref="uom.product_uom_meter"/>
            <field name="type">quantity</field>
            <field name="gs1_content_type">measure</field>
            <field name="gs1_decimal_usage">True</field>
        </record>

        <record id="barcode_rule_gs1_314y" model="barcode.rule">
            <field name="name">Area, square meters (variable measure trade item)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">314</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(314[0-5])(\d{6})</field>
            <field name="associated_uom_id" ref="uom.uom_square_meter"/>
            <field name="type">quantity</field>
            <field name="gs1_content_type">measure</field>
            <field name="gs1_decimal_usage">True</field>
        </record>

        <record id="barcode_rule_gs1_315y" model="barcode.rule">
            <field name="name">Net volume, litres (variable measure trade item)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">315</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(315[0-5])(\d{6})</field>
            <field name="associated_uom_id" ref="uom.product_uom_litre"/>
            <field name="type">quantity</field>
            <field name="gs1_content_type">measure</field>
            <field name="gs1_decimal_usage">True</field>
        </record>

        <record id="barcode_rule_gs1_316y" model="barcode.rule">
            <field name="name">Net volume, cubic metres (variable measure trade item)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">316</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(316[0-5])(\d{6})</field>
            <field name="associated_uom_id" ref="uom.product_uom_cubic_meter"/>
            <field name="type">quantity</field>
            <field name="gs1_content_type">measure</field>
            <field name="gs1_decimal_usage">True</field>
        </record>

        <record id="barcode_rule_gs1_320y" model="barcode.rule">
            <field name="name">Net weight, pounds (variable measure trade item)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">320</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(320[0-5])(\d{6})</field>
            <field name="associated_uom_id" ref="uom.product_uom_lb"/>
            <field name="type">quantity</field>
            <field name="gs1_content_type">measure</field>
            <field name="gs1_decimal_usage">True</field>
        </record>

        <record id="barcode_rule_gs1_321y" model="barcode.rule">
            <field name="name">Length or first dimension, inches (variable measure trade item)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">321</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(321[0-5])(\d{6})</field>
            <field name="associated_uom_id" ref="uom.product_uom_inch"/>
            <field name="type">quantity</field>
            <field name="gs1_content_type">measure</field>
            <field name="gs1_decimal_usage">True</field>
        </record>

        <record id="barcode_rule_gs1_322y" model="barcode.rule">
            <field name="name">Length or first dimension, feet (variable measure trade item)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">322</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(322[0-5])(\d{6})</field>
            <field name="associated_uom_id" ref="uom.product_uom_foot"/>
            <field name="type">quantity</field>
            <field name="gs1_content_type">measure</field>
            <field name="gs1_decimal_usage">True</field>
        </record>

        <record id="barcode_rule_gs1_323y" model="barcode.rule">
            <field name="name">Length or first dimension, yards (variable measure trade item)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">323</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(322[0-5])(\d{6})</field>
            <field name="associated_uom_id" ref="uom.product_uom_yard"/>
            <field name="type">quantity</field>
            <field name="gs1_content_type">measure</field>
            <field name="gs1_decimal_usage">True</field>
        </record>

        <record id="barcode_rule_gs1_351y" model="barcode.rule">
            <field name="name">Area, square feet (variable measure trade item)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">351</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(351[0-5])(\d{6})</field>
            <field name="associated_uom_id" ref="uom.uom_square_foot"/>
            <field name="type">quantity</field>
            <field name="gs1_content_type">measure</field>
            <field name="gs1_decimal_usage">True</field>
        </record>

        <record id="barcode_rule_gs1_357y" model="barcode.rule">
            <field name="name">Net weight (or volume), ounces (variable measure trade item)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">357</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(357[0-5])(\d{6})</field>
            <field name="associated_uom_id" ref="uom.product_uom_oz"/>
            <field name="type">quantity</field>
            <field name="gs1_content_type">measure</field>
            <field name="gs1_decimal_usage">True</field>
        </record>

        <record id="barcode_rule_gs1_360y" model="barcode.rule">
            <field name="name">Net volume, quarts (variable measure trade item)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">360</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(360[0-5])(\d{6})</field>
            <field name="associated_uom_id" ref="uom.product_uom_qt"/>
            <field name="type">quantity</field>
            <field name="gs1_content_type">measure</field>
            <field name="gs1_decimal_usage">True</field>
        </record>

        <record id="barcode_rule_gs1_361y" model="barcode.rule">
            <field name="name">Net volume, gallons U.S. (variable measure trade item)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">361</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(361[0-5])(\d{6})</field>
            <field name="associated_uom_id" ref="uom.product_uom_gal"/>
            <field name="type">quantity</field>
            <field name="gs1_content_type">measure</field>
            <field name="gs1_decimal_usage">True</field>
        </record>

        <record id="barcode_rule_gs1_364y" model="barcode.rule">
            <field name="name">Net volume, cubic inches (variable measure trade item)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">364</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(364[0-5])(\d{6})</field>
            <field name="associated_uom_id" ref="uom.product_uom_cubic_inch"/>
            <field name="type">quantity</field>
            <field name="gs1_content_type">measure</field>
            <field name="gs1_decimal_usage">True</field>
        </record>

        <record id="barcode_rule_gs1_365y" model="barcode.rule">
            <field name="name">Net volume, cubic feet (variable measure trade item)</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">365</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(365[0-5])(\d{6})</field>
            <field name="associated_uom_id" ref="uom.product_uom_cubic_foot"/>
            <field name="type">quantity</field>
            <field name="gs1_content_type">measure</field>
            <field name="gs1_decimal_usage">True</field>
        </record>

        <!-- Company internal information (91 to 99): Custom rules  -->
        <record id="barcode_rule_gs1_91" model="barcode.rule">
            <field name="name">Package type</field>
            <field name="barcode_nomenclature_id" ref="default_gs1_nomenclature"/>
            <field name="sequence">500</field>
            <field name="encoding">gs1-128</field>
            <field name="pattern">(91)([!"%-/0-9:-?A-Z_a-z]{0,90})</field>
            <field name="type">package_type</field>
            <field name="gs1_content_type">alpha</field>
        </record>
    </data>
</odoo>
