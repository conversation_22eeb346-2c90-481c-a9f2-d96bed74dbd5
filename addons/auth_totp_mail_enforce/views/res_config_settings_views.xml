<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.auth_totp_mail_enforce</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="40"/>
            <field name="inherit_id" ref="base_setup.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//setting[@id='allow_import']" position="before">
                    <setting id="auth_totp_policy" help="Enforce the two-factor authentication by email for employees or for all users (including portal users) if they didn't enable any other two-factor authentication method.">
                        <field name="auth_totp_enforce" />
                        <div class="mt16" invisible="not auth_totp_enforce">
                            <field name="auth_totp_policy" class="o_light_label" widget="radio"/>
                        </div>
                    </setting>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
