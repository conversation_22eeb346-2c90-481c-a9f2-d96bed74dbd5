<?xml version="1.0"?>
<odoo>
    <record id="auth_passkey_key_rename" model="ir.ui.view">
        <field name="name">auth.passkey.key.rename</field>
        <field name="model">auth.passkey.key</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="name"/>
                    </group>
                </sheet>
                <footer>
                    <button special="save" data-hotkey="s" string="Save" class="btn-primary"/>
                    <button special="cancel" data-hotkey="x" string="Cancel" class="btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="action_auth_passkey_key_create" model="ir.actions.act_window">
        <field name="name">Create Passkey Wizard</field>
        <field name="res_model">auth.passkey.key.create</field>
        <field name="target">new</field>
        <field name="view_mode">form</field>
    </record>

    <record id="auth_passkey_key_create_view_form" model="ir.ui.view">
        <field name="name">Create Passkey Wizard Form</field>
        <field name="model">auth.passkey.key.create</field>
        <field name="arch" type="xml">
            <form js_class="auth_passkey_key_create_view_form">
                <sheet>
                    <group>
                        <field name="name"/>
                    </group>
                </sheet>
                <footer>
                    <button name="make_key" type="object" string="Create" class="btn-primary" data-hotkey="q"/>
                    <button special="cancel" data-hotkey="x" string="Cancel" class="btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>
