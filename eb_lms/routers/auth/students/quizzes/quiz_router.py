# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Student Quiz API Router

Endpoints for student quiz taking:
- GET /quizzes: Get available quizzes for student
- GET /quizzes/{quiz_id}: Get quiz details and questions
- POST /quizzes/{quiz_id}/start: Start new quiz attempt
- POST /quizzes/{quiz_id}/submit: Submit quiz answers
- GET /quizzes/{quiz_id}/attempts: Get student's attempt history
- GET /quizzes/{quiz_id}/results: Get student's quiz results
"""

import logging
from typing import List, Optional
from datetime import datetime, timedelta
import pytz

from fastapi import APIRouter, Depends, HTTPException, Query, File, UploadFile, Form
from odoo import _
from odoo.addons.eb_lms.utils.helpers import safe_value
from odoo.exceptions import ValidationError, AccessError
import base64

from odoo.addons.eb_api_core.dependencies.auth import get_current_user
from odoo.addons.eb_api_core.schemas.base import ResponseBase
from odoo.addons.eb_lms.dependencies.role_dependencies import require_student

from .schemas import (
    QuizDetailSchema,
    QuizListItemSchema,
    QuizAttemptSchema,
    QuizResultSchema,
    StartQuizAttemptRequest,
    SubmitQuizAttemptRequest,
    StudentQuizzesResponse,
    StudentQuizAttemptsResponse,
    StudentQuizResultsResponse,
    AttemptStatusSchema,
    InProgressAttemptSchema,
    AttemptOptionsSchema,
    FileAttachmentSchema,
    QuizFileUploadRequest,
)

_logger = logging.getLogger(__name__)

router = APIRouter(prefix="/quizzes", tags=["Student Quizzes"])

# Lesson-specific quiz router
lesson_quiz_router = APIRouter(prefix="/lessons", tags=["Student Lesson Quiz"])


def convert_to_naive_datetime(dt):
    """
    Convert timezone-aware datetime to naive datetime in UTC.

    Args:
        dt: datetime object (can be timezone-aware or naive)

    Returns:
        datetime: naive datetime in UTC
    """
    if dt is None:
        return None

    if dt.tzinfo is not None:
        # Convert timezone-aware datetime to naive UTC datetime
        return dt.astimezone(pytz.UTC).replace(tzinfo=None)
    else:
        # Already naive datetime, return as is
        return dt


def build_attempt_status(quiz, student_attempts, can_attempt):
    """Helper function để build attempt status"""
    # Phân loại attempts
    in_progress_attempts = student_attempts.filtered(lambda a: a.state == "in_progress")
    completed_attempts = student_attempts.filtered(lambda a: a.state == "completed")

    # Thông tin attempt đang in_progress
    in_progress_attempt = in_progress_attempts[0] if in_progress_attempts else None
    in_progress_schema = None
    if in_progress_attempt:
        in_progress_schema = InProgressAttemptSchema(
            id=in_progress_attempt.id,
            attempt_number=in_progress_attempt.attempt_number,
            start_time=safe_value(in_progress_attempt.start_time),
            time_spent=safe_value(in_progress_attempt.time_spent),
        )

    # Tính toán các metrics
    total_attempts = len(student_attempts)
    completed_count = len(completed_attempts)
    best_score = max(completed_attempts.mapped('score')) if completed_attempts else None
    last_attempt_date = max(student_attempts.mapped('start_time')) if student_attempts else None

    # Xác định options
    can_continue = bool(in_progress_attempt)
    can_restart = bool(in_progress_attempt)  # Luôn có thể restart nếu có attempt đang dở
    can_start_new = bool(not in_progress_attempt and can_attempt)

    options = AttemptOptionsSchema(
        can_continue=can_continue,
        can_restart=can_restart,
        can_start_new=can_start_new,
        restart_warning="Làm lại sẽ mất tiến độ hiện tại" if can_restart else None,
    )

    return AttemptStatusSchema(
        has_in_progress=bool(in_progress_attempt),
        in_progress_attempt=in_progress_schema,
        total_attempts=total_attempts,
        completed_attempts=completed_count,
        best_score=best_score,
        last_attempt_date=last_attempt_date,
        options=options,
    )


@router.get(
    "/",
    response_model=ResponseBase[StudentQuizzesResponse],
    summary="Lấy danh sách quiz có thể làm",
    description="Lấy danh sách tất cả quiz mà học viên có thể truy cập trong các lớp đã đăng ký"
)
def get_student_quizzes(
    page: int = Query(1, ge=1, description="Số trang"),
    limit: int = Query(20, ge=1, le=100, description="Số item mỗi trang"),
    quiz_type: Optional[str] = Query(None, description="Lọc theo loại quiz"),
    subject_id: Optional[int] = Query(None, description="Lọc theo môn học"),
    class_id: Optional[int] = Query(None, description="Lọc theo lớp học"),
    state: Optional[str] = Query(None, description="Lọc theo trạng thái"),
    include_attempt_status: bool = Query(False, description="Bao gồm thông tin chi tiết về attempt"),
    current_user=Depends(get_current_user),
    _=Depends(require_student),
):
    """Lấy danh sách quiz có thể làm"""
    try:
        # Tìm student record
        student = current_user.env["eb.student.student"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)
        
        if not student:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin học viên"
            )
        
        # Lấy danh sách enrollment của học viên
        enrollments = current_user.env["eb.course.enrollment"].search([
            ("student_id", "=", student.id),
            ("state", "in", ["paid", "partial"])  # Chỉ enrollment đã thanh toán mới có thể truy cập quiz
        ])
        
        if not enrollments:
            return ResponseBase(
                success=True,
                message="Học viên chưa đăng ký lớp học nào",
                data=StudentQuizzesResponse(
                    quizzes=[],
                    total_count=0,
                    page=page,
                    limit=limit
                )
            )
        
        # Lấy danh sách class_id từ enrollments
        class_ids = enrollments.mapped("class_id").ids
        
        # Build domain cho quiz search - chỉ quiz đang được publish qua lesson_quiz
        domain = [
            ("is_currently_published", "=", True),  # Đang được công bố ở ít nhất 1 lesson
            ("state", "=", "ready"),  # Chỉ quiz đã sẵn sàng
            ("question_count", ">", 0),  # Chỉ quiz có câu hỏi
            "|",
            ("class_id", "in", class_ids),  # Quiz của lớp học viên đăng ký
            ("class_id", "=", False),  # Hoặc quiz không giới hạn lớp
        ]
        
        # Thêm filters
        if quiz_type:
            domain.append(("quiz_type", "=", quiz_type))
        if subject_id:
            domain.append(("subject_id", "=", subject_id))
        if class_id:
            domain.append(("class_id", "=", class_id))
        
        # Tính offset
        offset = (page - 1) * limit
        
        # Lấy quizzes
        quizzes = current_user.env["eb.quiz"].search(
            domain, 
            limit=limit, 
            offset=offset,
            order="create_date desc"
        )
        
        total_count = current_user.env["eb.quiz"].search_count(domain)
        
        # Convert to schema
        quiz_items = []
        for quiz in quizzes:
            # Lấy thông tin attempt của học viên cho quiz này
            student_attempts = current_user.env["eb.quiz.attempt"].search([
                ("quiz_id", "=", quiz.id),
                ("student_id", "=", student.id)
            ])
            
            student_results = current_user.env["eb.quiz.result"].search([
                ("quiz_id", "=", quiz.id),
                ("student_id", "=", student.id)
            ], limit=1)
            
            # Kiểm tra có thể làm bài không
            can_attempt = True

            # Kiểm tra thời gian
            now = datetime.now()
            if quiz.start_date and now < quiz.start_date:
                can_attempt = False
            if quiz.end_date and now > quiz.end_date:
                can_attempt = False
            
            # Build attempt status nếu được yêu cầu
            attempt_status = None
            if include_attempt_status:
                attempt_status = build_attempt_status(quiz, student_attempts, can_attempt)

            quiz_item = QuizListItemSchema(
                id=quiz.id,
                name=quiz.name,
                code=quiz.code,
                quiz_type=quiz.quiz_type,
                subject_name=quiz.subject_id.name if quiz.subject_id else None,
                class_name=quiz.class_id.name if quiz.class_id else None,
                instructor_name=quiz.instructor_id.name if quiz.instructor_id else None,
                max_score=quiz.max_score,
                passing_score=quiz.passing_score,
                time_limit=safe_value(quiz.time_limit),
                question_count=quiz.question_count,
                start_date=quiz.start_date if quiz.start_date else None,
                end_date=quiz.end_date if quiz.end_date else None,
                state=quiz.state,
                student_attempt_count=len(student_attempts),
                student_best_score=student_results.final_score if student_results else None,
                student_last_attempt=student_attempts[-1].start_time if student_attempts else None,
                can_attempt=can_attempt,
                attempt_status=attempt_status,
            )
            quiz_items.append(quiz_item)
        
        return ResponseBase(
            success=True,
            message=f"Lấy danh sách quiz thành công. Tìm thấy {total_count} quiz.",
            data=StudentQuizzesResponse(
                quizzes=quiz_items,
                total_count=total_count,
                page=page,
                limit=limit
            )
        )
        
    except Exception as e:
        _logger.error(f"Lỗi khi lấy danh sách quiz: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )


@router.get(
    "/{quiz_id}",
    response_model=ResponseBase[QuizDetailSchema],
    summary="Lấy chi tiết quiz",
    description="Lấy chi tiết quiz bao gồm câu hỏi và đáp án (nếu được phép)"
)
def get_quiz_detail(
    quiz_id: int,
    include_questions: bool = Query(False, description="Có bao gồm câu hỏi không"),
    include_attempt_status: bool = Query(False, description="Bao gồm thông tin chi tiết về attempt"),
    current_user=Depends(get_current_user),
    _=Depends(require_student),
):
    """Lấy chi tiết quiz"""
    try:
        # Tìm student record
        student = current_user.env["eb.student.student"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)
        
        if not student:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin học viên"
            )
        
        # Lấy quiz
        quiz = current_user.env["eb.quiz"].browse(quiz_id)
        if not quiz.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy quiz"
            )
        
        # Kiểm tra quyền truy cập - quiz phải được publish qua lesson_quiz
        if not quiz.is_currently_published:
            raise HTTPException(
                status_code=403,
                detail="Quiz chưa được công bố cho buổi học nào"
            )
        
        # Kiểm tra học viên có trong lớp hoặc khóa học không (nếu quiz có giới hạn lớp)
        if quiz.class_id:
            enrollment = current_user.env["eb.course.enrollment"].search([
                ("student_id", "=", student.id),
                "|",  # OR condition
                ("class_id", "=", quiz.class_id.id),
                ("course_id", "=", quiz.class_id.course_id.id),
                ("state", "in", ["enrolled", "active", "paid"])
            ], limit=1)

            if not enrollment:
                raise HTTPException(
                    status_code=403,
                    detail="Bạn không có quyền truy cập quiz này"
                )
        
        # Helper function to convert False to None for optional fields
        def safe_value(value):
            return None if value is False else value

        # Convert to schema
        quiz_detail = QuizDetailSchema(
            id=quiz.id,
            name=quiz.name,
            code=quiz.code,
            description=safe_value(quiz.description),
            instruction=safe_value(quiz.instruction),
            quiz_type=quiz.quiz_type,
            subject_name=quiz.subject_id.name if quiz.subject_id else None,
            course_name=quiz.course_id.name if quiz.course_id else None,
            class_name=quiz.class_id.name if quiz.class_id else None,
            instructor_name=quiz.instructor_id.name if quiz.instructor_id else None,
            max_score=quiz.max_score,
            passing_score=quiz.passing_score,
            time_limit=safe_value(quiz.time_limit),
            start_date=safe_value(quiz.start_date),
            end_date=safe_value(quiz.end_date),
            is_randomized=quiz.is_randomized,
            show_correct_answers=quiz.show_correct_answers,
            show_result_immediately=quiz.show_result_immediately,
            state=quiz.state,
            is_published=quiz.is_published,
            question_count=quiz.question_count,
            attempt_count=quiz.attempt_count,
            student_count=quiz.student_count,
            average_score=quiz.average_score,
            pass_rate=quiz.pass_rate,
        )
        
        # Thêm câu hỏi nếu được yêu cầu
        if include_questions:
            questions = []
            question_records = quiz.question_ids.sorted('sequence')
            
            for question in question_records:
                # Lấy đáp án cho câu hỏi trắc nghiệm
                answer_options = []
                if question.question_type in ['single_choice', 'multiple_choice', 'true_false']:
                    for answer in question.answer_ids.sorted('sequence'):
                        answer_options.append({
                            "id": answer.id,
                            "text": answer.name,
                            "sequence": answer.sequence,
                        })
                
                questions.append({
                    "id": question.id,
                    "name": question.name,
                    "question_type": question.question_type,
                    "score": question.score,
                    "difficulty": question.difficulty,
                    "sequence": question.sequence,
                    "image": None,  # TODO: Handle image if needed
                    "explanation": question.explanation if quiz.show_correct_answers else None,
                    "is_required": question.is_required,
                    "answer_options": answer_options if answer_options else None,
                })
            
            quiz_detail.questions = questions

        # Thêm attempt status nếu được yêu cầu
        if include_attempt_status:
            # Lấy thông tin attempt của học viên cho quiz này
            student_attempts = current_user.env["eb.quiz.attempt"].search([
                ("quiz_id", "=", quiz.id),
                ("student_id", "=", student.id)
            ])

            # Kiểm tra có thể làm bài không
            can_attempt = True

            # Kiểm tra thời gian
            now = datetime.now()
            if quiz.start_date and now < quiz.start_date:
                can_attempt = False
            if quiz.end_date and now > quiz.end_date:
                can_attempt = False

            # Build attempt status
            quiz_detail.attempt_status = build_attempt_status(quiz, student_attempts, can_attempt)

        return ResponseBase(
            success=True,
            message="Lấy chi tiết quiz thành công",
            data=quiz_detail
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Lỗi khi lấy chi tiết quiz {quiz_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )


@router.post(
    "/{quiz_id}/start",
    response_model=ResponseBase[QuizAttemptSchema],
    summary="Bắt đầu hoặc tiếp tục quiz attempt",
    description="Bắt đầu attempt mới, tiếp tục attempt cũ, hoặc restart attempt"
)
def start_quiz_attempt(
    quiz_id: int,
    action: str = Query("auto", description="Hành động: auto|continue|restart|new"),
    current_user=Depends(get_current_user),
    _=Depends(require_student),
):
    """Bắt đầu làm bài quiz"""
    try:
        # Tìm student record
        student = current_user.env["eb.student.student"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not student:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin học viên"
            )

        # Lấy quiz
        quiz = current_user.env["eb.quiz"].browse(quiz_id)
        if not quiz.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy quiz"
            )

        # Kiểm tra quyền truy cập - quiz phải được publish qua lesson_quiz
        if not quiz.is_currently_published:
            raise HTTPException(
                status_code=403,
                detail="Quiz chưa được công bố cho buổi học nào"
            )

        # Kiểm tra thời gian
        now = datetime.now()
        if quiz.start_date and now < quiz.start_date:
            raise HTTPException(
                status_code=403,
                detail="Quiz chưa đến thời gian bắt đầu"
            )
        if quiz.end_date and now > quiz.end_date:
            raise HTTPException(
                status_code=403,
                detail="Quiz đã hết thời gian"
            )

        # Lấy danh sách attempts hiện có
        existing_attempts = current_user.env["eb.quiz.attempt"].search([
            ("quiz_id", "=", quiz.id),
            ("student_id", "=", student.id)
        ])

        # Kiểm tra có attempt đang in_progress không
        in_progress_attempt = current_user.env["eb.quiz.attempt"].search([
            ("quiz_id", "=", quiz.id),
            ("student_id", "=", student.id),
            ("state", "=", "in_progress")
        ], limit=1)

        # Kiểm tra đã có attempt thành công chưa
        passed_attempt = current_user.env["eb.quiz.attempt"].search([
            ("quiz_id", "=", quiz.id),
            ("student_id", "=", student.id),
            ("is_passed", "=", True)
        ], limit=1)

        # Helper function để tạo attempt mới
        def create_new_attempt():
            # Kiểm tra đã pass quiz chưa trước khi tạo attempt mới
            if passed_attempt:
                raise HTTPException(
                    status_code=403,
                    detail="Bạn đã hoàn thành quiz này thành công, không thể làm lại"
                )

            attempt_vals = {
                "quiz_id": quiz.id,
                "student_id": student.id,
                "attempt_number": len(existing_attempts) + 1,
                "start_time": datetime.now(),
                "state": "in_progress",
                "max_score": quiz.max_score,
            }
            return current_user.env["eb.quiz.attempt"].create(attempt_vals)

        # Xử lý theo action
        if action == "auto":
            # Current behavior: return existing or create new
            if in_progress_attempt:
                attempt = in_progress_attempt
                message = "Tiếp tục làm bài"
            else:
                attempt = create_new_attempt()
                message = "Bắt đầu làm bài thành công"

        elif action == "continue":
            if not in_progress_attempt:
                raise HTTPException(
                    status_code=400,
                    detail="Không có attempt nào để tiếp tục"
                )
            attempt = in_progress_attempt
            message = "Tiếp tục làm bài"

        elif action == "restart":
            if not in_progress_attempt:
                raise HTTPException(
                    status_code=400,
                    detail="Không có attempt nào để restart"
                )

            # Mark current attempt as completed with 0 score (abandoned)
            in_progress_attempt.write({
                "state": "completed",
                "end_time": datetime.now(),
                "score": 0.0,
                "percentage": 0.0,
                "is_passed": False,
            })

            # Create new attempt
            attempt = create_new_attempt()
            message = "Bắt đầu làm bài mới"

        elif action == "new":
            if in_progress_attempt:
                raise HTTPException(
                    status_code=400,
                    detail="Có attempt đang dở, không thể tạo mới. Sử dụng action=restart để làm lại."
                )
            attempt = create_new_attempt()
            message = "Bắt đầu làm bài thành công"

        else:
            raise HTTPException(
                status_code=400,
                detail="Action không hợp lệ. Sử dụng: auto, continue, restart, hoặc new"
            )

        # Helper function to convert False to None for optional fields
        def safe_value(value):
            return None if value is False else value

        # Tính thời gian còn lại
        remaining_time = None
        if quiz.time_limit:
            elapsed_minutes = (datetime.now() - attempt.start_time).total_seconds() / 60
            remaining_time = int(max(0, quiz.time_limit - elapsed_minutes))  # Convert to int

        # Convert to schema
        attempt_schema = QuizAttemptSchema(
            id=attempt.id,
            quiz_id=attempt.quiz_id.id,
            quiz_name=attempt.quiz_id.name,
            attempt_number=attempt.attempt_number,
            start_time=safe_value(attempt.start_time),
            end_time=safe_value(attempt.end_time),  # Handle False -> None
            time_spent=safe_value(attempt.time_spent),
            remaining_time=remaining_time,
            score=attempt.score,
            max_score=attempt.max_score,
            percentage=attempt.percentage,
            is_passed=attempt.is_passed,
            state=attempt.state,
            feedback=safe_value(attempt.feedback),  # Handle False -> None
        )

        return ResponseBase(
            success=True,
            message=message,
            data=attempt_schema
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Lỗi khi bắt đầu quiz {quiz_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )


@router.post(
    "/{quiz_id}/submit",
    response_model=ResponseBase[QuizAttemptSchema],
    summary="Nộp bài quiz",
    description="Nộp câu trả lời và hoàn thành attempt"
)
def submit_quiz_attempt(
    quiz_id: int,
    request: SubmitQuizAttemptRequest,
    current_user=Depends(get_current_user),
    _=Depends(require_student),
):
    """Nộp bài quiz"""
    try:
        # Tìm student record
        student = current_user.env["eb.student.student"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not student:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin học viên"
            )

        # Lấy quiz
        quiz = current_user.env["eb.quiz"].browse(quiz_id)
        if not quiz.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy quiz"
            )

        # Tìm attempt đang in_progress
        attempt = current_user.env["eb.quiz.attempt"].search([
            ("quiz_id", "=", quiz.id),
            ("student_id", "=", student.id),
            ("state", "=", "in_progress")
        ], limit=1)

        if not attempt:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy attempt đang làm bài"
            )

        # Convert timezone-aware datetime to naive datetime
        submit_time_naive = convert_to_naive_datetime(request.submit_time)

        # Kiểm tra thời gian (nếu có time limit)
        if quiz.time_limit:
            elapsed_minutes = (submit_time_naive - attempt.start_time).total_seconds() / 60
            if elapsed_minutes > quiz.time_limit:
                raise HTTPException(
                    status_code=403,
                    detail="Đã hết thời gian làm bài"
                )

        # Lưu câu trả lời
        for answer_data in request.answers:
            # Tìm hoặc tạo attempt answer
            attempt_answer = current_user.env["eb.quiz.attempt.answer"].search([
                ("attempt_id", "=", attempt.id),
                ("question_id", "=", answer_data.question_id)
            ], limit=1)

            answer_vals = {
                "attempt_id": attempt.id,
                "question_id": answer_data.question_id,
                "quiz_id": quiz.id,
                "student_id": student.id,
            }

            # Xử lý theo loại câu hỏi
            if answer_data.question_type == "single_choice":
                # Single choice: lấy answer_id đầu tiên từ answer_ids
                if answer_data.answer_ids and len(answer_data.answer_ids) > 0:
                    answer_vals["answer_id"] = answer_data.answer_ids[0]
                elif answer_data.answer_id:
                    answer_vals["answer_id"] = answer_data.answer_id
            elif answer_data.question_type == "multiple_choice":
                # Multiple choice: tạo nhiều records cho mỗi answer_id
                if answer_data.answer_ids:
                    # Tạo record đầu tiên với answer_id đầu tiên
                    answer_vals["answer_id"] = answer_data.answer_ids[0]
                    # Các answer_id còn lại sẽ được tạo riêng
            elif answer_data.question_type in ["essay", "fill_in_blank"]:
                answer_vals["text_answer"] = answer_data.text_answer

                # Handle file attachments for essay questions
                if answer_data.question_type == "essay" and answer_data.attachment_ids:
                    # Validate attachments belong to this attempt and are valid
                    valid_attachments = current_user.env["ir.attachment"].search([
                        ("id", "in", answer_data.attachment_ids),
                        ("res_model", "=", "eb.quiz.attempt.answer"),
                    ])

                    if valid_attachments:
                        answer_vals["attachment_ids"] = [(6, 0, valid_attachments.ids)]
            elif answer_data.question_type == "true_false":
                if answer_data.answer_ids and len(answer_data.answer_ids) > 0:
                    answer_vals["answer_id"] = answer_data.answer_ids[0]
                elif answer_data.answer_id:
                    answer_vals["answer_id"] = answer_data.answer_id

            # Tạo hoặc cập nhật attempt answer
            if attempt_answer:
                attempt_answer.write(answer_vals)
            else:
                current_user.env["eb.quiz.attempt.answer"].create(answer_vals)

            # Xử lý multiple choice: tạo thêm records cho các answer_ids còn lại
            if answer_data.question_type == "multiple_choice" and answer_data.answer_ids and len(answer_data.answer_ids) > 1:
                for answer_id in answer_data.answer_ids[1:]:  # Bỏ qua answer_id đầu tiên đã tạo
                    additional_answer_vals = {
                        "attempt_id": attempt.id,
                        "question_id": answer_data.question_id,
                        "quiz_id": quiz.id,
                        "student_id": student.id,
                        "answer_id": answer_id,
                    }
                    current_user.env["eb.quiz.attempt.answer"].create(additional_answer_vals)

        # Cập nhật attempt
        attempt.write({
            "end_time": submit_time_naive,
            "state": "completed",
        })

        # Tính điểm tự động cho trắc nghiệm
        total_score = 0.0
        max_possible_score = 0.0

        # Lấy tất cả questions của quiz
        questions = quiz.question_ids

        for question in questions:
            max_possible_score += question.score

            # Lấy answers của student cho question này
            student_answers = current_user.env["eb.quiz.attempt.answer"].search([
                ("attempt_id", "=", attempt.id),
                ("question_id", "=", question.id)
            ])

            if question.question_type in ["single_choice", "true_false"]:
                # Single choice & True/False: chỉ cần 1 đáp án đúng
                if student_answers and student_answers[0].answer_id:
                    correct_answer = current_user.env["eb.quiz.answer"].search([
                        ("question_id", "=", question.id),
                        ("is_correct", "=", True)
                    ], limit=1)

                    if correct_answer and student_answers[0].answer_id.id == correct_answer.id:
                        total_score += question.score
                        student_answers[0].write({"is_correct": True, "score": question.score})
                    else:
                        student_answers[0].write({"is_correct": False, "score": 0.0})

            elif question.question_type == "multiple_choice":
                # Multiple choice: cần tất cả đáp án đúng
                correct_answers = current_user.env["eb.quiz.answer"].search([
                    ("question_id", "=", question.id),
                    ("is_correct", "=", True)
                ])

                student_answer_ids = set(student_answers.mapped("answer_id").ids)
                correct_answer_ids = set(correct_answers.ids)

                if student_answer_ids == correct_answer_ids:
                    # Tất cả đáp án đúng
                    total_score += question.score
                    student_answers.write({"is_correct": True, "score": question.score / len(student_answers)})
                else:
                    # Có đáp án sai hoặc thiếu
                    student_answers.write({"is_correct": False, "score": 0.0})

            elif question.question_type == "fill_in_blank":
                # Fill in blank: auto-check với keyword matching
                if student_answers and student_answers[0].text_answer:
                    student_text = student_answers[0].text_answer.strip().lower()

                    # Danh sách keywords chấp nhận cho từng câu hỏi
                    # Có thể lưu trong database hoặc config
                    accepted_keywords = {
                        36: ["mê kông", "mekong", "cửu long"]  # Question ID 36
                    }

                    if question.id in accepted_keywords:
                        keywords = accepted_keywords[question.id]
                        is_correct = any(keyword in student_text for keyword in keywords)

                        if is_correct:
                            total_score += question.score
                            student_answers[0].write({"is_correct": True, "score": question.score})
                        else:
                            student_answers[0].write({"is_correct": False, "score": 0.0})
                    else:
                        # Không có keywords định nghĩa -> cần manual grading
                        student_answers[0].write({"is_correct": None, "score": 0.0})

        # Tính phần trăm và kết quả
        percentage = (total_score / max_possible_score * 100) if max_possible_score > 0 else 0
        is_passed = percentage >= quiz.passing_score

        # Cập nhật attempt với điểm số
        attempt.write({
            "score": total_score,
            "percentage": percentage,
            "is_passed": is_passed,
            "end_time": submit_time_naive,
            "state": "completed",
        })

        # Tạo hoặc cập nhật result
        result = current_user.env["eb.quiz.result"].search([
            ("quiz_id", "=", quiz.id),
            ("student_id", "=", student.id)
        ], limit=1)

        if result:
            # Cập nhật result với attempt tốt nhất
            best_score = max(result.final_score, attempt.score)
            result.write({
                "attempt_count": result.attempt_count + 1,
                "final_score": best_score,
                "percentage": (best_score / quiz.max_score * 100) if quiz.max_score > 0 else 0,
                "is_passed": (best_score / quiz.max_score * 100) >= quiz.passing_score if quiz.max_score > 0 else False,
                "completion_date": submit_time_naive,
                "state": "completed",
                "best_attempt_id": attempt.id if attempt.score > result.final_score else result.best_attempt_id,
            })
        else:
            current_user.env["eb.quiz.result"].create({
                "quiz_id": quiz.id,
                "student_id": student.id,
                "attempt_count": 1,
                "final_score": attempt.score,
                "percentage": attempt.percentage,
                "is_passed": attempt.is_passed,
                "completion_date": submit_time_naive,
                "state": "completed",
                "best_attempt_id": attempt.id,
            })

        # Helper function to convert False to None for optional fields
        def safe_value(value):
            return None if value is False else value

        # Convert to schema
        attempt_schema = QuizAttemptSchema(
            id=attempt.id,
            quiz_id=attempt.quiz_id.id,
            quiz_name=attempt.quiz_id.name,
            attempt_number=attempt.attempt_number,
            start_time=safe_value(attempt.start_time),
            end_time=safe_value(attempt.end_time),
            time_spent=safe_value(attempt.time_spent),
            remaining_time=0,
            score=attempt.score,
            max_score=attempt.max_score,
            percentage=attempt.percentage,
            is_passed=attempt.is_passed,
            state=attempt.state,
            feedback=safe_value(attempt.feedback),
        )

        return ResponseBase(
            success=True,
            message="Nộp bài thành công",
            data=attempt_schema
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Lỗi khi nộp bài quiz {quiz_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )


@router.get(
    "/{quiz_id}/attempts",
    response_model=ResponseBase[StudentQuizAttemptsResponse],
    summary="Lấy lịch sử làm bài",
    description="Lấy danh sách tất cả lần làm bài của học viên cho quiz này"
)
def get_quiz_attempts(
    quiz_id: int,
    current_user=Depends(get_current_user),
    _=Depends(require_student),
):
    """Lấy lịch sử làm bài"""
    try:
        # Tìm student record
        student = current_user.env["eb.student.student"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not student:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin học viên"
            )

        # Lấy quiz
        quiz = current_user.env["eb.quiz"].browse(quiz_id)
        if not quiz.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy quiz"
            )

        # Lấy danh sách attempts
        attempts = current_user.env["eb.quiz.attempt"].search([
            ("quiz_id", "=", quiz.id),
            ("student_id", "=", student.id)
        ], order="attempt_number desc")

        # Helper function to convert False to None for optional fields
        def safe_value(value):
            return None if value is False else value

        # Convert to schema
        attempt_items = []
        for attempt in attempts:
            # Tính thời gian còn lại nếu đang in_progress
            remaining_time = None
            if attempt.state == "in_progress" and quiz.time_limit:
                elapsed_minutes = (datetime.now() - attempt.start_time).total_seconds() / 60
                remaining_time = int(max(0, quiz.time_limit - elapsed_minutes))

            attempt_schema = QuizAttemptSchema(
                id=attempt.id,
                quiz_id=attempt.quiz_id.id,
                quiz_name=attempt.quiz_id.name,
                attempt_number=attempt.attempt_number,
                start_time=safe_value(attempt.start_time),
                end_time=safe_value(attempt.end_time),
                time_spent=safe_value(attempt.time_spent),
                remaining_time=remaining_time,
                score=attempt.score,
                max_score=attempt.max_score,
                percentage=attempt.percentage,
                is_passed=attempt.is_passed,
                state=attempt.state,
                feedback=safe_value(attempt.feedback),
            )
            attempt_items.append(attempt_schema)

        return ResponseBase(
            success=True,
            message=f"Lấy lịch sử làm bài thành công. Tìm thấy {len(attempt_items)} lần làm bài.",
            data=StudentQuizAttemptsResponse(
                attempts=attempt_items,
                total_count=len(attempt_items)
            )
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Lỗi khi lấy lịch sử làm bài quiz {quiz_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )


@router.get(
    "/{quiz_id}/results",
    response_model=ResponseBase[QuizResultSchema],
    summary="Lấy kết quả quiz",
    description="Lấy kết quả cuối cùng của học viên cho quiz này"
)
def get_quiz_result(
    quiz_id: int,
    current_user=Depends(get_current_user),
    _=Depends(require_student),
):
    """Lấy kết quả quiz"""
    try:
        # Tìm student record
        student = current_user.env["eb.student.student"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not student:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin học viên"
            )

        # Lấy quiz
        quiz = current_user.env["eb.quiz"].browse(quiz_id)
        if not quiz.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy quiz"
            )

        # Lấy kết quả
        result = current_user.env["eb.quiz.result"].search([
            ("quiz_id", "=", quiz.id),
            ("student_id", "=", student.id)
        ], limit=1)

        if not result:
            raise HTTPException(
                status_code=404,
                detail="Chưa có kết quả cho quiz này"
            )

        # Convert to schema
        result_schema = QuizResultSchema(
            id=result.id,
            quiz_id=result.quiz_id.id,
            quiz_name=result.quiz_id.name,
            attempt_count=result.attempt_count,
            final_score=result.final_score,
            max_score=result.quiz_id.max_score,
            percentage=result.percentage,
            is_passed=result.is_passed,
            completion_date=safe_value(result.completion_date),
            state=result.state,
            best_attempt_id=result.best_attempt_id.id if result.best_attempt_id else None,
        )

        return ResponseBase(
            success=True,
            message="Lấy kết quả quiz thành công",
            data=result_schema
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Lỗi khi lấy kết quả quiz {quiz_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )


@router.get(
    "/lessons/{lesson_id}/quiz",
    response_model=ResponseBase[Optional[QuizDetailSchema]],
    summary="Kiểm tra buổi học có quiz không",
    description="Kiểm tra xem buổi học có quiz và lấy thông tin quiz nếu có"
)
def get_lesson_quiz(
    lesson_id: int,
    current_user=Depends(get_current_user),
    _=Depends(require_student),
):
    """Kiểm tra buổi học có quiz không"""
    try:
        # Tìm student record
        student = current_user.env["eb.student.student"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not student:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin học viên"
            )

        # Lấy lesson
        lesson = current_user.env["eb.lesson.lesson"].browse(lesson_id)
        if not lesson.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy buổi học"
            )

        # Kiểm tra học viên có trong lớp hoặc khóa học không
        enrollment = current_user.env["eb.course.enrollment"].search([
            ("student_id", "=", student.id),
            "|",  # OR condition
            ("class_id", "=", lesson.class_id.id),
            ("course_id", "=", lesson.class_id.course_id.id),
            ("state", "in", ["enrolled", "active", "paid"])
        ], limit=1)

        if not enrollment:
            raise HTTPException(
                status_code=403,
                detail="Bạn không có quyền truy cập buổi học này"
            )

        # Kiểm tra có quiz không thông qua lesson_quiz junction table
        lesson_quizzes = current_user.env["eb.lesson.quiz"].search([
            ("lesson_id", "=", lesson.id),
            ("is_active", "=", True)
        ])

        if not lesson_quizzes:
            return ResponseBase(
                success=True,
                message="Buổi học này không có quiz",
                data=None
            )

        # Lấy quiz đầu tiên (có thể có nhiều quiz cho 1 lesson)
        quiz = lesson_quizzes[0].quiz_id

        # Kiểm tra quyền truy cập quiz - sử dụng is_currently_published thay vì state
        if not quiz.is_currently_published:
            return ResponseBase(
                success=True,
                message="Quiz chưa được công bố",
                data=None
            )

        # Handle Odoo False values
        start_date = quiz.start_date if quiz.start_date is not False else None
        end_date = quiz.end_date if quiz.end_date is not False else None
        description = quiz.description if quiz.description is not False else None
        instruction = quiz.instruction if quiz.instruction is not False else None

        # Convert to schema
        quiz_detail = QuizDetailSchema(
            id=quiz.id,
            name=quiz.name,
            code=quiz.code,
            description=description,
            instruction=instruction,
            quiz_type=quiz.quiz_type,
            subject_name=quiz.subject_id.name if quiz.subject_id else None,
            course_name=quiz.course_id.name if quiz.course_id else None,
            class_name=quiz.class_id.name if quiz.class_id else None,
            instructor_name=quiz.instructor_id.name if quiz.instructor_id else None,
            max_score=quiz.max_score,
            passing_score=quiz.passing_score,
            time_limit=safe_value(quiz.time_limit),
            start_date=start_date,
            end_date=end_date,
            is_randomized=quiz.is_randomized,
            show_correct_answers=quiz.show_correct_answers,
            show_result_immediately=quiz.show_result_immediately,
            state=quiz.state,
            is_published=quiz.is_published,
            question_count=quiz.question_count,
            attempt_count=quiz.attempt_count,
            student_count=quiz.student_count,
            average_score=quiz.average_score,
            pass_rate=quiz.pass_rate,
        )

        return ResponseBase(
            success=True,
            message="Buổi học có quiz",
            data=quiz_detail
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Lỗi khi kiểm tra quiz cho lesson {lesson_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )


@lesson_quiz_router.get(
    "/{lesson_id}/quizzes",
    response_model=ResponseBase[List],
    summary="Get Lesson Quizzes for Student",
    description="Get active quizzes for a specific lesson"
)
def get_lesson_quizzes_for_student(
    lesson_id: int,
    current_user=Depends(get_current_user),
    _=Depends(require_student),
):
    """Lấy danh sách quiz đang hoạt động của buổi học"""
    try:
        # Tìm student record
        student = current_user.env["eb.student.student"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not student:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin học viên"
            )

        # Lấy lesson
        lesson = current_user.env["eb.lesson.lesson"].browse(lesson_id)
        if not lesson.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy buổi học"
            )

        # Kiểm tra quyền truy cập lesson (học viên phải thuộc lớp hoặc khóa học)
        student_enrollments = current_user.env["eb.course.enrollment"].search([
            ("student_id", "=", student.id),
            "|",  # OR condition
            ("class_id", "=", lesson.class_id.id),
            ("course_id", "=", lesson.class_id.course_id.id),
            ("state", "in", ["enrolled", "active", "paid"])
        ])

        if not student_enrollments:
            raise HTTPException(
                status_code=403,
                detail="Bạn không có quyền truy cập buổi học này"
            )

        # Lấy active quiz của lesson
        active_lesson_quizzes = lesson.lesson_quiz_ids.filtered('is_active')

        quiz_items = []
        for lesson_quiz in active_lesson_quizzes:
            quiz = lesson_quiz.quiz_id

            # Lấy thông tin attempt của student
            student_attempts = current_user.env["eb.quiz.attempt"].search([
                ("quiz_id", "=", quiz.id),
                ("student_id", "=", student.id),
            ])

            attempts_count = len(student_attempts)
            best_score = max(student_attempts.mapped('score')) if student_attempts else None
            last_attempt = student_attempts.sorted('start_time', reverse=True)[:1]
            last_attempt_at = last_attempt.start_time if last_attempt else None

            # Luôn có thể làm bài (không giới hạn số lần thử)
            can_attempt = True

            quiz_item = {
                "id": lesson_quiz.id,
                "quiz_id": quiz.id,
                "quiz_name": quiz.name,
                "quiz_type": quiz.quiz_type,
                "quiz_description": quiz.description,
                "quiz_instruction": quiz.instruction,
                "sequence": lesson_quiz.sequence,
                "is_active": lesson_quiz.is_active,
                "published_at": lesson_quiz.published_at,
                "quiz_question_count": quiz.question_count,
                "quiz_max_score": quiz.max_score,
                "quiz_time_limit": safe_value(quiz.time_limit),
                "quiz_passing_score": quiz.passing_score,
                "student_attempts_count": attempts_count,
                "student_best_score": best_score,
                "student_last_attempt_at": last_attempt_at,
                "can_attempt": can_attempt,
            }
            quiz_items.append(quiz_item)

        # Sort by sequence
        quiz_items.sort(key=lambda x: x["sequence"])

        return ResponseBase(
            success=True,
            message="Lấy danh sách quiz của buổi học thành công",
            data=quiz_items,
            meta={
                "lesson_id": lesson.id,
                "lesson_name": lesson.name,
                "lesson_start_datetime": safe_value(lesson.start_datetime).isoformat() if safe_value(lesson.start_datetime) else None if lesson.start_datetime else None,
                "total_quizzes": len(quiz_items),
                "can_attempt_count": len([q for q in quiz_items if q["can_attempt"]]),
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Get lesson quizzes for student error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi hệ thống: {str(e)}"
        )


@router.post(
    "/{quiz_id}/attempts/{attempt_id}/upload",
    response_model=ResponseBase[FileAttachmentSchema],
    summary="Upload file cho câu trả lời tự luận",
    description="Upload file đính kèm cho câu hỏi essay trong quiz attempt"
)
async def upload_quiz_answer_file(
    quiz_id: int,
    attempt_id: int,
    question_id: int = Form(..., description="ID câu hỏi"),
    file: UploadFile = File(..., description="File to upload"),
    description: Optional[str] = Form(None, description="Mô tả file"),
    current_user=Depends(get_current_user),
    _=Depends(require_student),
):
    """Upload file cho câu trả lời tự luận"""
    try:
        # Validate file size (max 10MB for quiz files)
        file_content = await file.read()
        if len(file_content) > 10 * 1024 * 1024:
            raise HTTPException(
                status_code=400,
                detail="Kích thước file không được vượt quá 10MB"
            )

        # Validate file type (allow common document types)
        allowed_types = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'image/jpeg',
            'image/png',
            'image/gif'
        ]

        if file.content_type not in allowed_types:
            raise HTTPException(
                status_code=400,
                detail="Loại file không được hỗ trợ. Chỉ chấp nhận: PDF, Word, Text, JPG, PNG, GIF"
            )

        # Find student
        student = current_user.env["eb.student.student"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not student:
            raise HTTPException(status_code=404, detail="Không tìm thấy thông tin học viên")

        # Validate attempt belongs to student
        attempt = current_user.env["eb.quiz.attempt"].search([
            ("id", "=", attempt_id),
            ("quiz_id", "=", quiz_id),
            ("student_id", "=", student.id),
            ("state", "=", "in_progress")
        ], limit=1)

        if not attempt:
            raise HTTPException(status_code=404, detail="Không tìm thấy attempt hợp lệ")

        # Validate question is essay type
        question = current_user.env["eb.quiz.question"].search([
            ("id", "=", question_id),
            ("quiz_id", "=", quiz_id),
            ("question_type", "=", "essay")
        ], limit=1)

        if not question:
            raise HTTPException(status_code=404, detail="Không tìm thấy câu hỏi tự luận")

        # Create attachment
        attachment = current_user.env["ir.attachment"].create({
            "name": file.filename,
            "datas": base64.b64encode(file_content),
            "type": "binary",
            "mimetype": file.content_type,
            "res_model": "eb.quiz.attempt.answer",
            "description": description or f"File đính kèm cho câu hỏi {question_id}",
        })

        # Find or create attempt answer
        attempt_answer = current_user.env["eb.quiz.attempt.answer"].search([
            ("attempt_id", "=", attempt.id),
            ("question_id", "=", question_id)
        ], limit=1)

        if not attempt_answer:
            attempt_answer = current_user.env["eb.quiz.attempt.answer"].create({
                "attempt_id": attempt.id,
                "question_id": question_id,
                "quiz_id": quiz_id,
                "student_id": student.id,
                "state": "answered",
            })

        # Link attachment to answer
        attempt_answer.write({
            "attachment_ids": [(4, attachment.id)]
        })

        # Generate URLs
        base_url = f"/web/content/{attachment.id}"
        download_url = f"{base_url}?download=true"
        preview_url = f"{base_url}" if attachment.mimetype.startswith('image/') else None

        # Return file info
        file_schema = FileAttachmentSchema(
            id=attachment.id,
            name=attachment.name,
            size=attachment.file_size,
            mimetype=attachment.mimetype,
            access_token=attachment.access_token or "",
            url=base_url,
            preview_url=preview_url,
            uploaded_at=safe_value(attachment.create_date),
        )

        return ResponseBase(
            success=True,
            message="Upload file thành công",
            data=file_schema
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Lỗi khi upload file cho quiz {quiz_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Lỗi server: {str(e)}")


@router.delete(
    "/{quiz_id}/attempts/{attempt_id}/attachments/{attachment_id}",
    response_model=ResponseBase[dict],
    summary="Xóa file đính kèm",
    description="Xóa file đính kèm khỏi câu trả lời"
)
def delete_quiz_answer_file(
    quiz_id: int,
    attempt_id: int,
    attachment_id: int,
    current_user=Depends(get_current_user),
    _=Depends(require_student),
):
    """Xóa file đính kèm khỏi câu trả lời"""
    try:
        # Find student
        student = current_user.env["eb.student.student"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not student:
            raise HTTPException(status_code=404, detail="Không tìm thấy thông tin học viên")

        # Validate attempt belongs to student
        attempt = current_user.env["eb.quiz.attempt"].search([
            ("id", "=", attempt_id),
            ("quiz_id", "=", quiz_id),
            ("student_id", "=", student.id),
            ("state", "=", "in_progress")
        ], limit=1)

        if not attempt:
            raise HTTPException(status_code=404, detail="Không tìm thấy attempt hợp lệ")

        # Find attachment
        attachment = current_user.env["ir.attachment"].search([
            ("id", "=", attachment_id)
        ], limit=1)

        if not attachment:
            raise HTTPException(status_code=404, detail="Không tìm thấy file")

        # Remove attachment from all answers in this attempt
        attempt_answers = current_user.env["eb.quiz.attempt.answer"].search([
            ("attempt_id", "=", attempt.id),
            ("attachment_ids", "in", [attachment_id])
        ])

        for answer in attempt_answers:
            answer.write({
                "attachment_ids": [(3, attachment_id)]  # Remove link
            })

        # Delete attachment
        attachment.unlink()

        return ResponseBase(
            success=True,
            message="Xóa file thành công",
            data={}
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Lỗi khi xóa file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Lỗi server: {str(e)}")
